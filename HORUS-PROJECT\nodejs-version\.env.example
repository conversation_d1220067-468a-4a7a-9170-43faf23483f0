# HORUS AI Agent - Node.js Version Configuration

# Server Configuration
PORT=8000
HOST=0.0.0.0
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=*

# Ollama Configuration
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=mistral:7b
OLLAMA_TIMEOUT=30000

# Memory Configuration
MEMORY_DIR=./data/memory
MAX_CONVERSATION_LENGTH=50
MAX_MEMORY_SIZE=1000

# Logging Configuration
LOG_LEVEL=info
LOG_DIR=./logs
LOG_CONSOLE=true
LOG_FILE=true

# File Upload Configuration
UPLOAD_DIR=./temp/uploads
MAX_FILE_SIZE=10485760
MAX_FILES=1

# Security Configuration
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Performance Configuration
CLUSTER_MODE=false
CLUSTER_WORKERS=0

# Features Configuration
ENABLE_FILE_ANALYSIS=true
ENABLE_AI_ANALYSIS=true
ENABLE_WEBSOCKET=true
ENABLE_METRICS=true

# Database Configuration (if needed in future)
# DATABASE_URL=sqlite:./data/horus.db

# API Keys (if needed for external services)
# OPENAI_API_KEY=
# ANTHROPIC_API_KEY=

# Monitoring Configuration
# SENTRY_DSN=
# PROMETHEUS_PORT=9090
