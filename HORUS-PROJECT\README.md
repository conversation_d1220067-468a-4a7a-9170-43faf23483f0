# 🤖 HORUS AI Agent

<div align="center">

![HORUS Logo](https://img.shields.io/badge/HORUS-AI%20Agent-blue?style=for-the-badge&logo=robot)
![Python](https://img.shields.io/badge/Python-3.8+-green?style=for-the-badge&logo=python)
![Docker](https://img.shields.io/badge/Docker-Ready-blue?style=for-the-badge&logo=docker)
![<PERSON><PERSON><PERSON>](https://img.shields.io/badge/Ollama-Mistral%207B-orange?style=for-the-badge)

**وكيل ذكي محلي متقدم مع نموذج Mistral 7B**

[🚀 البدء السريع](#-البدء-السريع) • [📖 التوثيق](#-التوثيق) • [🐳 Docker](#-docker) • [🛠️ التطوير](#️-التطوير)

</div>

---

## 📋 جدول المحتويات

- [🎯 نظرة عامة](#-نظرة-عامة)
- [✨ المميزات](#-المميزات)
- [🔧 المتطلبات](#-المتطلبات)
- [🚀 البدء السريع](#-البدء-السريع)
- [🐳 Docker](#-docker)
- [📖 التوثيق](#-التوثيق)
- [🛠️ التطوير](#️-التطوير)
- [📊 الأداء](#-الأداء)
- [🤝 المساهمة](#-المساهمة)

---

## 🎯 نظرة عامة

**HORUS AI Agent** هو وكيل ذكي متقدم يعمل محلياً باستخدام نموذج **Mistral 7B** عبر **Ollama**. يوفر قدرات تحليل وذكاء اصطناعي متقدمة مع الحفاظ على الخصوصية الكاملة.

### 🌟 لماذا HORUS؟

- 🔒 **خصوصية كاملة** - جميع البيانات تبقى على جهازك
- ⚡ **أداء عالي** - محسن للأجهزة المحلية
- 🧠 **ذكاء متقدم** - نموذج Mistral 7B القوي
- 🌐 **واجهات متعددة** - Web UI + API + CLI
- 🐳 **سهولة النشر** - Docker ready

---

## ✨ المميزات

### 🤖 الذكاء الاصطناعي
- **نموذج Mistral 7B** محلي
- **فهم العربية والإنجليزية**
- **ذاكرة محادثة متقدمة**
- **تحليل وتلخيص النصوص**

### 🔧 التحليل والأدوات
- **تحليل هيكل المشاريع**
- **فحص الكود والملفات**
- **إنشاء التقارير**
- **مراقبة الموارد**

### 🌐 الواجهات
- **Web Interface** - واجهة ويب تفاعلية
- **REST API** - واجهة برمجية شاملة
- **CLI Tools** - أدوات سطر الأوامر
- **WebSocket** - اتصال فوري

### 🐳 النشر
- **Docker Compose** - نشر بنقرة واحدة
- **Auto-scaling** - تحجيم تلقائي
- **Health Monitoring** - مراقبة الصحة
- **Backup & Restore** - نسخ احتياطي

---

## 🔧 المتطلبات

### 💻 متطلبات النظام

| المكون | الحد الأدنى | الموصى به |
|--------|-------------|------------|
| **RAM** | 8 GB | 16+ GB |
| **CPU** | 4 cores | 8+ cores |
| **Storage** | 10 GB | 50+ GB |
| **GPU** | اختياري | NVIDIA (CUDA) |

### 🛠️ متطلبات البرمجيات

- **Python** 3.8+ (للتشغيل المباشر)
- **Docker** & **Docker Compose** (للحاوية)
- **Ollama** (للنموذج المحلي)

---

## 🚀 البدء السريع

### 🐳 الطريقة الأسرع - Docker

```bash
# 1. استنساخ المشروع
git clone <repository-url>
cd HORUS-PROJECT

# 2. تشغيل بنقرة واحدة (Windows)
run_docker.bat

# أو (Linux/Mac)
./run_docker.sh
```

### 🐍 التشغيل المباشر

```bash
# 1. فحص المتطلبات
python check_requirements.py

# 2. تثبيت المكتبات
pip install -r requirements.txt

# 3. تشغيل HORUS
python quick_start.py
```

### 🌐 الوصول للنظام

- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/status

---

## 🐳 Docker

### 🚀 التشغيل السريع

```bash
# الإنتاج
docker-compose -f DEPLOYMENT/docker/docker-compose.yml up -d

# التطوير
docker-compose -f DEPLOYMENT/docker/docker-compose.dev.yml up -d

# مع Nginx
docker-compose -f DEPLOYMENT/docker/docker-compose.yml --profile with-nginx up -d
```

### 🛠️ إدارة الحاوية

```bash
# حالة النظام
./DEPLOYMENT/scripts/manage.sh status

# عرض السجلات
./DEPLOYMENT/scripts/manage.sh logs

# إعادة التشغيل
./DEPLOYMENT/scripts/manage.sh restart

# النسخ الاحتياطي
./DEPLOYMENT/scripts/manage.sh backup
```

### 📊 مراقبة الموارد

```bash
# مراقبة مستمرة
./DEPLOYMENT/scripts/manage.sh monitor

# فحص سريع
python UTILS/monitor_resources.py check
```

---

## 📖 التوثيق

### 📁 هيكل المشروع

```
HORUS-PROJECT/
├── 🧠 CORE/                 # النواة الأساسية
│   ├── agent/              # وكيل HORUS
│   ├── memory/             # نظام الذاكرة
│   └── api/                # واجهة API
├── 🔍 ANALYZERS/           # محللات متخصصة
├── ⚙️ CONFIG/              # ملفات الإعداد
├── 🛠️ UTILS/               # أدوات مساعدة
├── 🐳 DEPLOYMENT/          # ملفات النشر
│   ├── docker/             # Docker files
│   └── scripts/            # سكريبتات النشر
└── 📊 REPORTS/             # تقارير النظام
```

### 🔌 API Endpoints

| Endpoint | Method | الوصف |
|----------|--------|--------|
| `/api/status` | GET | حالة النظام |
| `/api/chat` | POST | محادثة مع الوكيل |
| `/api/analyze` | POST | تحليل الملفات |
| `/api/memory` | GET | استعراض الذاكرة |
| `/docs` | GET | توثيق API |

### 📝 أمثلة الاستخدام

```python
import requests

# محادثة مع HORUS
response = requests.post('http://localhost:8000/api/chat', json={
    'message': 'مرحبا، كيف يمكنك مساعدتي؟',
    'use_tools': True
})

print(response.json()['response'])
```

---

## 🛠️ التطوير

### 🔧 إعداد بيئة التطوير

```bash
# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows

# تثبيت المكتبات
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### 🧪 تشغيل الاختبارات

```bash
# اختبارات شاملة
python -m pytest tests/

# اختبار مكون محدد
python -m pytest tests/test_agent.py

# مع تغطية الكود
python -m pytest --cov=CORE tests/
```

### 🔍 فحص الكود

```bash
# فحص الجودة
flake8 CORE/
black CORE/
isort CORE/

# فحص الأمان
bandit -r CORE/
```

---

## 📊 الأداء

### ⚡ معايير الأداء

| المقياس | القيمة | الملاحظات |
|---------|--------|-----------|
| **وقت البدء** | 8-15 ثانية | أول تشغيل |
| **الاستجابة** | 2-8 ثواني | حسب تعقيد السؤال |
| **استهلاك RAM** | 8-12 GB | مع النموذج |
| **استهلاك CPU** | 20-60% | أثناء المعالجة |

### 🎯 تحسين الأداء

```bash
# مراقبة الأداء
python UTILS/monitor_resources.py

# تحسين الذاكرة
export OLLAMA_KEEP_ALIVE=5m

# استخدام GPU
export OLLAMA_GPU=1
```

---

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. **Fork** المشروع
2. إنشاء **branch** جديد (`git checkout -b feature/amazing-feature`)
3. **Commit** التغييرات (`git commit -m 'Add amazing feature'`)
4. **Push** للـ branch (`git push origin feature/amazing-feature`)
5. فتح **Pull Request**

### 📋 إرشادات المساهمة

- اتبع معايير الكود الموجودة
- أضف اختبارات للميزات الجديدة
- حدث التوثيق عند الحاجة
- تأكد من نجاح جميع الاختبارات

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 شكر وتقدير

- **Ollama** - لتوفير منصة النماذج المحلية
- **Mistral AI** - لنموذج Mistral 7B الرائع
- **FastAPI** - لإطار العمل السريع
- **Docker** - لتسهيل النشر

---

<div align="center">

**صنع بـ ❤️ للمجتمع العربي**

[⭐ Star](../../stargazers) • [🐛 Report Bug](../../issues) • [💡 Request Feature](../../issues)

</div>
