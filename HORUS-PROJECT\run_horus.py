#!/usr/bin/env python3
"""
HORUS AI Agent - Updated Main Entry Point
نقطة الدخول المحدثة لوكيل HORUS الذكي
"""
import sys
import subprocess
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))


def main():
    """Main entry point"""
    print("🤖 HORUS AI Agent v1.0.0")
    print("=" * 50)
    
    # Check if Ollama is available
    try:
        result = subprocess.run(
            ["ollama", "--version"], 
            capture_output=True, 
            text=True, 
            timeout=5
        )
        if result.returncode != 0:
            print("❌ Ollama not found. Please install Ollama first.")
            print("   Visit: https://ollama.ai")
            return
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Ollama not found. Please install Ollama first.")
        print("   Visit: https://ollama.ai")
        return
    
    # Try to import and run CLI
    try:
        from CORE.api.cli import app as cli_app
        cli_app()
    except ImportError as e:
        print(f"❌ Could not import CLI from new structure: {e}")
        print("🔄 Trying alternative import...")
        try:
            # Fallback to old structure
            import sys
            sys.path.append(str(Path(__file__).parent / "CORE" / "api"))
            from cli import app as cli_app
            cli_app()
        except ImportError as e2:
            print(f"❌ Alternative import also failed: {e2}")
            print("Please check the project structure and dependencies.")
            return


if __name__ == "__main__":
    main()
