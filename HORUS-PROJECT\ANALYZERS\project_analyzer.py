#!/usr/bin/env python3
"""
🦅 HORUS - محلل المشاريع الرئيسي (نظام منفصل)
Main Project Analyzer for HORUS Independent System
"""

import os
import sys
import json
import uuid
from pathlib import Path
from datetime import datetime
from collections import defaultdict

# استيراد المحللات المتخصصة
try:
    from .python_analyzer import HorusPythonAnalyzer
except ImportError:
    HorusPythonAnalyzer = None

class HorusProjectAnalyzer:
    """محلل المشاريع الرئيسي لنظام HORUS المنفصل"""
    
    def __init__(self, project_path=None):
        self.project_path = Path(project_path) if project_path else Path(".").resolve()
        self.project_name = self.project_path.name
        self.analysis_id = str(uuid.uuid4())
        self.timestamp = datetime.now()
        
        # مسار نظام HORUS
        self.horus_root = Path(__file__).parent.parent
        
        # تهيئة النتائج
        self.results = {
            "metadata": {
                "analysis_id": self.analysis_id,
                "timestamp": self.timestamp.isoformat(),
                "analyzer_version": "HORUS 1.0.0 (Independent)",
                "project_name": self.project_name,
                "project_path": str(self.project_path),
                "horus_location": str(self.horus_root),
                "system_status": "INDEPENDENT"
            }
        }
        
        # أنماط اكتشاف أنواع المشاريع
        self.project_indicators = {
            "Python": {
                "files": ["requirements.txt", "setup.py", "pyproject.toml", "Pipfile"],
                "extensions": [".py"],
                "directories": ["venv", "env", "__pycache__"]
            },
            "Node.js": {
                "files": ["package.json", "package-lock.json", "yarn.lock"],
                "extensions": [".js", ".ts", ".jsx", ".tsx"],
                "directories": ["node_modules", "dist", "build"]
            },
            "Java": {
                "files": ["pom.xml", "build.gradle", "gradlew"],
                "extensions": [".java", ".class", ".jar"],
                "directories": ["src/main/java", "target", "build"]
            },
            "C#": {
                "files": [".csproj", ".sln", "packages.config"],
                "extensions": [".cs", ".dll", ".exe"],
                "directories": ["bin", "obj", "packages"]
            },
            "PHP": {
                "files": ["composer.json", "composer.lock"],
                "extensions": [".php"],
                "directories": ["vendor", "public"]
            },
            "Ruby": {
                "files": ["Gemfile", "Gemfile.lock", "Rakefile"],
                "extensions": [".rb"],
                "directories": ["gems", "vendor/bundle"]
            },
            "Go": {
                "files": ["go.mod", "go.sum"],
                "extensions": [".go"],
                "directories": ["vendor"]
            },
            "Rust": {
                "files": ["Cargo.toml", "Cargo.lock"],
                "extensions": [".rs"],
                "directories": ["target", "src"]
            },
            "Docker": {
                "files": ["Dockerfile", "docker-compose.yml", "docker-compose.yaml"],
                "extensions": [],
                "directories": []
            },
            "Web": {
                "files": ["index.html", "index.htm"],
                "extensions": [".html", ".htm", ".css"],
                "directories": ["css", "js", "assets", "static"]
            }
        }
    
    def run_full_analysis(self):
        """تشغيل التحليل الكامل"""
        print(f"🦅 HORUS - نظام منفصل لتحليل المشاريع")
        print(f"📁 المشروع: {self.project_name}")
        print(f"📍 المسار: {self.project_path}")
        print(f"🏠 موقع HORUS: {self.horus_root}")
        print(f"🆔 معرف التحليل: {self.analysis_id}")
        print("=" * 60)

        # فحص إذا كان مشروع Python واستخدام المحلل المتخصص
        detected_types = self.detect_project_types()
        is_python_project = any(pt.get('type') == 'Python' for pt in detected_types)

        if is_python_project and HorusPythonAnalyzer:
            print("🐍 تم اكتشاف مشروع Python - استخدام المحلل المتقدم...")
            return self.run_python_analysis()
        else:
            return self.run_general_analysis()

    def run_python_analysis(self):
        """تشغيل التحليل المتقدم لمشروع Python"""
        try:
            # استخدام محلل Python المتقدم
            python_analyzer = HorusPythonAnalyzer(self.project_path)
            python_results = python_analyzer.analyze_full_python_project()

            # دمج النتائج مع التحليل العام
            general_results = self.run_general_analysis()

            # دمج النتائج
            combined_results = general_results.copy()
            combined_results["python_analysis"] = python_results
            combined_results["analyzer_type"] = "Python Advanced"

            return combined_results

        except Exception as e:
            print(f"❌ خطأ في التحليل المتقدم لـ Python: {e}")
            return self.run_general_analysis()

    def run_general_analysis(self):
        """تشغيل التحليل العام"""
        
        try:
            # 1. تحليل الهيكل
            print("🏗️ تحليل هيكل المشروع...")
            structure_results = self.analyze_structure()
            self.results["structure"] = structure_results
            print("✅ تحليل الهيكل مكتمل")
            
            # 2. تحليل التبعيات
            print("📦 تحليل التبعيات...")
            dependency_results = self.analyze_dependencies()
            self.results["dependencies"] = dependency_results
            print("✅ تحليل التبعيات مكتمل")
            
            # 3. تحليل الجودة
            print("📊 تحليل جودة الكود...")
            quality_results = self.analyze_quality()
            self.results["quality"] = quality_results
            print("✅ تحليل الجودة مكتمل")
            
            # 4. إنشاء التوصيات
            print("💡 إنشاء التوصيات...")
            recommendations = self.generate_recommendations()
            self.results["recommendations"] = recommendations
            print("✅ التوصيات مكتملة")
            
            # 5. حفظ في الذاكرة
            print("🧠 حفظ في الذاكرة...")
            self.save_to_memory()
            print("✅ تم الحفظ في الذاكرة")
            
            # 6. إنشاء التقرير
            print("📄 إنشاء التقرير...")
            report_path = self.save_report()
            print(f"✅ التقرير محفوظ: {report_path}")
            
            # 7. عرض الملخص
            self.display_summary()
            
            return self.results
            
        except Exception as e:
            print(f"❌ خطأ في التحليل: {e}")
            return None
    
    def analyze_structure(self):
        """تحليل هيكل المشروع"""
        # اكتشاف نوع المشروع
        project_types = self.detect_project_types()
        
        # إحصائيات الملفات
        total_files = 0
        total_dirs = 0
        file_types = defaultdict(int)
        large_files = []
        
        for item in self.project_path.rglob("*"):
            if item.is_file():
                total_files += 1
                extension = item.suffix.lower() or "no_extension"
                file_types[extension] += 1
                
                # فحص الملفات الكبيرة
                try:
                    size_mb = item.stat().st_size / (1024 * 1024)
                    if size_mb > 1:
                        large_files.append({
                            "path": str(item.relative_to(self.project_path)),
                            "size_mb": round(size_mb, 2)
                        })
                except:
                    pass
            elif item.is_dir():
                total_dirs += 1
        
        return {
            "project_name": self.project_name,
            "project_path": str(self.project_path),
            "project_types": project_types,
            "file_statistics": {
                "total_files": total_files,
                "total_directories": total_dirs,
                "file_types": dict(sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]),
                "large_files": large_files[:10]
            }
        }
    
    def detect_project_types(self):
        """اكتشاف أنواع المشروع"""
        detected_types = []
        
        for project_type, indicators in self.project_indicators.items():
            score = 0
            
            # فحص الملفات المميزة
            for file_indicator in indicators["files"]:
                if (self.project_path / file_indicator).exists():
                    score += 3
            
            # فحص الامتدادات
            for ext in indicators["extensions"]:
                if list(self.project_path.rglob(f"*{ext}")):
                    score += 2
            
            # فحص المجلدات المميزة
            for dir_indicator in indicators["directories"]:
                if (self.project_path / dir_indicator).exists():
                    score += 1
            
            if score >= 2:
                detected_types.append({
                    "type": project_type,
                    "confidence": min(score * 10, 100),
                    "indicators_found": score
                })
        
        detected_types.sort(key=lambda x: x["confidence"], reverse=True)
        return detected_types
    
    def analyze_dependencies(self):
        """تحليل التبعيات"""
        dependencies = {}
        
        # Python dependencies
        req_file = self.project_path / "requirements.txt"
        if req_file.exists():
            try:
                with open(req_file, 'r', encoding='utf-8') as f:
                    python_deps = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                dependencies["Python"] = python_deps[:20]
            except:
                pass
        
        # Node.js dependencies
        package_file = self.project_path / "package.json"
        if package_file.exists():
            try:
                with open(package_file, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                    node_deps = list(package_data.get('dependencies', {}).keys())
                    dev_deps = list(package_data.get('devDependencies', {}).keys())
                dependencies["Node.js"] = {
                    "dependencies": node_deps[:15],
                    "devDependencies": dev_deps[:15]
                }
            except:
                pass
        
        return dependencies
    
    def analyze_quality(self):
        """تحليل جودة الكود"""
        doc_files = 0
        test_files = 0
        code_files = 0
        config_files = 0
        
        doc_patterns = ["readme", "changelog", "license", "contributing"]
        test_patterns = ["test", "spec", "__test__"]
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file():
                file_name = file_path.name.lower()
                file_ext = file_path.suffix.lower()
                
                # ملفات التوثيق
                if any(pattern in file_name for pattern in doc_patterns) or file_ext in [".md", ".rst", ".txt"]:
                    doc_files += 1
                # ملفات الاختبار
                elif any(pattern in file_name for pattern in test_patterns):
                    test_files += 1
                # ملفات الكود
                elif file_ext in [".py", ".js", ".ts", ".java", ".cs", ".php", ".rb", ".go", ".rs"]:
                    code_files += 1
                # ملفات التكوين
                elif file_ext in [".json", ".yml", ".yaml", ".toml", ".ini", ".cfg"]:
                    config_files += 1
        
        total_files = doc_files + test_files + code_files + config_files
        
        # حساب النسب
        doc_ratio = (doc_files / total_files * 100) if total_files > 0 else 0
        test_ratio = (test_files / total_files * 100) if total_files > 0 else 0
        
        # تقييم الجودة
        quality_score = 0
        if doc_ratio > 10: quality_score += 25
        elif doc_ratio > 5: quality_score += 15
        
        if test_ratio > 20: quality_score += 30
        elif test_ratio > 10: quality_score += 20
        elif test_ratio > 5: quality_score += 10
        
        if code_files > 0: quality_score += 20
        if config_files > 0: quality_score += 10
        
        quality_grade = "ممتاز" if quality_score >= 80 else "جيد" if quality_score >= 60 else "مقبول" if quality_score >= 40 else "يحتاج تحسين"
        
        return {
            "file_categories": {
                "documentation_files": doc_files,
                "test_files": test_files,
                "code_files": code_files,
                "config_files": config_files,
                "total_files": total_files
            },
            "quality_metrics": {
                "doc_ratio": f"{doc_ratio:.1f}%",
                "test_ratio": f"{test_ratio:.1f}%",
                "overall_quality_score": quality_score,
                "quality_grade": quality_grade
            }
        }
    
    def generate_recommendations(self):
        """إنشاء التوصيات"""
        recommendations = []
        
        structure = self.results.get("structure", {})
        quality = self.results.get("quality", {})
        
        project_types = [pt.get("type") for pt in structure.get("project_types", [])]
        quality_score = quality.get("quality_metrics", {}).get("overall_quality_score", 0)
        
        # توصيات عامة
        if quality_score < 60:
            recommendations.append({
                "type": "quality",
                "priority": "high",
                "title": "تحسين جودة المشروع",
                "description": f"نقاط الجودة منخفضة ({quality_score}/100)"
            })
        
        # توصيات حسب نوع المشروع
        if "Python" in project_types:
            recommendations.extend([
                {
                    "type": "python",
                    "priority": "medium",
                    "title": "استخدام Virtual Environment",
                    "description": "إنشاء بيئة افتراضية للمشروع"
                },
                {
                    "type": "python",
                    "priority": "low",
                    "title": "تنسيق الكود",
                    "description": "استخدام Black لتنسيق الكود"
                }
            ])
        
        if "Node.js" in project_types:
            recommendations.extend([
                {
                    "type": "nodejs",
                    "priority": "medium",
                    "title": "فحص الكود",
                    "description": "استخدام ESLint لفحص جودة الكود"
                }
            ])
        
        return recommendations
    
    def save_to_memory(self):
        """حفظ التحليل في الذاكرة"""
        memory_file = self.horus_root / "MEMORY" / "projects_database.json"
        
        try:
            # قراءة الذاكرة الحالية
            if memory_file.exists():
                with open(memory_file, 'r', encoding='utf-8') as f:
                    memory_data = json.load(f)
            else:
                memory_data = {"horus_memory": {"total_projects_analyzed": 0}, "projects": {}}
            
            # إضافة المشروع الجديد
            project_key = f"{self.project_name}_{hash(str(self.project_path)) % 10000}"
            memory_data["projects"][project_key] = {
                "name": self.project_name,
                "path": str(self.project_path),
                "last_analyzed": self.timestamp.isoformat(),
                "analysis_id": self.analysis_id,
                "summary": {
                    "project_types": [pt.get("type") for pt in self.results.get("structure", {}).get("project_types", [])],
                    "quality_score": self.results.get("quality", {}).get("quality_metrics", {}).get("overall_quality_score", 0)
                }
            }
            
            # تحديث الإحصائيات
            memory_data["horus_memory"]["total_projects_analyzed"] = len(memory_data["projects"])
            memory_data["horus_memory"]["last_updated"] = self.timestamp.isoformat()
            
            # حفظ الذاكرة
            with open(memory_file, 'w', encoding='utf-8') as f:
                json.dump(memory_data, f, ensure_ascii=False, indent=2)
        
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم حفظ الذاكرة - {e}")
    
    def save_report(self):
        """حفظ التقرير"""
        reports_dir = self.horus_root / "REPORTS" / "json"
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp_str = self.timestamp.strftime("%Y%m%d_%H%M%S")
        filename = f"horus_analysis_{self.project_name}_{timestamp_str}.json"
        report_path = reports_dir / filename
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        return report_path
    
    def display_summary(self):
        """عرض ملخص النتائج"""
        print("\n🦅 HORUS - ملخص التحليل (نظام منفصل)")
        print("=" * 50)
        
        structure = self.results.get("structure", {})
        quality = self.results.get("quality", {})
        recommendations = self.results.get("recommendations", [])
        
        print(f"📁 اسم المشروع: {structure.get('project_name', 'غير محدد')}")
        
        project_types = structure.get('project_types', [])
        if project_types:
            types_str = ", ".join([f"{pt.get('type', 'غير محدد')} ({pt.get('confidence', 0)}%)" for pt in project_types[:3]])
            print(f"🏷️ نوع المشروع: {types_str}")
        
        file_stats = structure.get('file_statistics', {})
        print(f"📄 إجمالي الملفات: {file_stats.get('total_files', 0):,}")
        print(f"📂 إجمالي المجلدات: {file_stats.get('total_directories', 0):,}")
        
        quality_metrics = quality.get('quality_metrics', {})
        print(f"📊 نقاط الجودة: {quality_metrics.get('overall_quality_score', 0)}/100")
        print(f"🏆 تقييم الجودة: {quality_metrics.get('quality_grade', 'غير محدد')}")
        print(f"💡 عدد التوصيات: {len(recommendations)}")
        
        # عرض أهم التوصيات
        high_priority = [r for r in recommendations if r.get('priority') == 'high']
        if high_priority:
            print(f"\n🔴 أهم التوصيات:")
            for i, rec in enumerate(high_priority[:3], 1):
                print(f"  {i}. {rec.get('title', 'غير محدد')}")

def main():
    """الدالة الرئيسية"""
    import argparse
    
    parser = argparse.ArgumentParser(description="🦅 HORUS - محلل المشاريع المتقدم (نظام منفصل)")
    parser.add_argument("--project", "-p", help="مسار المشروع")
    
    args = parser.parse_args()
    
    # تحديد مسار المشروع
    project_path = args.project if args.project else "."
    
    # إنشاء المحلل
    analyzer = HorusProjectAnalyzer(project_path)
    
    # تشغيل التحليل
    results = analyzer.run_full_analysis()
    
    if results:
        print(f"\n🎉 تم الانتهاء من التحليل بنجاح!")
        print(f"🆔 معرف التحليل: {results['metadata']['analysis_id']}")
        print(f"🏠 نظام HORUS منفصل في: {results['metadata']['horus_location']}")
    else:
        print(f"\n❌ فشل في التحليل")

if __name__ == "__main__":
    main()
