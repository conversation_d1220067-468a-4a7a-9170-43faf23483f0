"""
Configuration settings for HORUS AI Agent
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Ollama Configuration
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "mistral:7b"
    OLLAMA_TIMEOUT: int = 120
    
    # Agent Configuration
    AGENT_NAME: str = "HORUS"
    AGENT_VERSION: str = "1.0.0"
    MAX_CONVERSATION_HISTORY: int = 50
    
    # API Configuration
    API_HOST: str = "localhost"
    API_PORT: int = 8000
    API_RELOAD: bool = True
    
    # Database Configuration
    DATABASE_URL: str = "sqlite:///./horus_agent.db"
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "horus_agent.log"
    
    # Security
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # File Paths
    WORKSPACE_PATH: str = os.getcwd()
    MEMORY_FILE: str = "CORE/memory/agent_memory.json"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
