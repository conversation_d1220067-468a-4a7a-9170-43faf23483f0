#!/usr/bin/env python3
"""
🦅 HORUS - مح<PERSON>ل Python المتقدم (نظام منفصل)
Advanced Python Analyzer for HORUS Independent System
"""

import os
import sys
import ast
import re
import json
import subprocess
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime

class HorusPythonAnalyzer:
    """محلل Python المتقدم لنظام HORUS المنفصل"""
    
    def __init__(self, project_path=None):
        self.project_path = Path(project_path) if project_path else Path(".").resolve()
        self.project_name = self.project_path.name
        
        # Python-specific patterns
        self.python_patterns = {
            "frameworks": {
                "Django": ["django", "manage.py", "settings.py", "urls.py"],
                "Flask": ["flask", "app.py", "application.py"],
                "FastAPI": ["fastapi", "main.py"],
                "Streamlit": ["streamlit", "app.py"],
                "Jupyter": [".ipynb", "jupyter"],
                "Pytest": ["pytest", "test_", "_test.py"],
                "Pandas": ["pandas", "pd"],
                "NumPy": ["numpy", "np"],
                "Matplotlib": ["matplotlib", "plt"],
                "Scikit-learn": ["sklearn", "scikit-learn"],
                "TensorFlow": ["tensorflow", "tf"],
                "PyTorch": ["torch", "pytorch"]
            },
            "config_files": [
                "requirements.txt", "setup.py", "pyproject.toml", 
                "Pipfile", "environment.yml", "conda.yml",
                "setup.cfg", "tox.ini", ".flake8", "pytest.ini"
            ],
            "virtual_envs": ["venv", "env", ".venv", ".env", "virtualenv"],
            "package_dirs": ["__pycache__", "*.egg-info", "dist", "build"]
        }
        
        # Code quality patterns
        self.quality_patterns = {
            "docstring_patterns": [
                r'""".*?"""',
                r"'''.*?'''",
                r'r""".*?"""',
                r"r'''.*?'''"
            ],
            "comment_patterns": [
                r'#.*$'
            ],
            "import_patterns": [
                r'^import\s+([a-zA-Z_][a-zA-Z0-9_\.]*)',
                r'^from\s+([a-zA-Z_][a-zA-Z0-9_\.]*)\s+import'
            ]
        }
    
    def analyze_full_python_project(self):
        """تحليل شامل لمشروع Python"""
        print(f"🐍 بدء التحليل المتقدم لمشروع Python: {self.project_name}")
        
        results = {
            "metadata": {
                "project_name": self.project_name,
                "project_path": str(self.project_path),
                "analysis_timestamp": datetime.now().isoformat(),
                "analyzer": "HORUS Python Analyzer v1.0"
            },
            "project_structure": self.analyze_project_structure(),
            "dependencies": self.analyze_dependencies(),
            "code_quality": self.analyze_code_quality(),
            "frameworks_detected": self.detect_frameworks(),
            "virtual_environment": self.analyze_virtual_environment(),
            "testing": self.analyze_testing_setup(),
            "documentation": self.analyze_documentation(),
            "performance_indicators": self.analyze_performance_indicators(),
            "security_analysis": self.analyze_security(),
            "recommendations": []
        }
        
        # إنشاء التوصيات
        results["recommendations"] = self.generate_recommendations(results)
        
        return results
    
    def analyze_project_structure(self):
        """تحليل هيكل مشروع Python"""
        structure = {
            "python_files": [],
            "packages": [],
            "modules": [],
            "scripts": [],
            "config_files": [],
            "total_lines": 0,
            "file_statistics": {}
        }
        
        for py_file in self.project_path.rglob("*.py"):
            relative_path = py_file.relative_to(self.project_path)
            
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = len(content.splitlines())
                    structure["total_lines"] += lines
                
                file_info = {
                    "path": str(relative_path),
                    "lines": lines,
                    "size_kb": py_file.stat().st_size / 1024,
                    "type": self.classify_python_file(py_file, content)
                }
                
                structure["python_files"].append(file_info)
                
                # تصنيف الملف
                if file_info["type"] == "package":
                    structure["packages"].append(str(relative_path.parent))
                elif file_info["type"] == "module":
                    structure["modules"].append(str(relative_path))
                elif file_info["type"] == "script":
                    structure["scripts"].append(str(relative_path))
                    
            except Exception as e:
                continue
        
        # إحصائيات الملفات
        structure["file_statistics"] = {
            "total_python_files": len(structure["python_files"]),
            "total_packages": len(set(structure["packages"])),
            "total_modules": len(structure["modules"]),
            "total_scripts": len(structure["scripts"]),
            "average_file_size": sum(f["lines"] for f in structure["python_files"]) / max(len(structure["python_files"]), 1)
        }
        
        return structure
    
    def classify_python_file(self, file_path, content):
        """تصنيف ملف Python"""
        file_name = file_path.name
        
        # فحص إذا كان __init__.py
        if file_name == "__init__.py":
            return "package"
        
        # فحص إذا كان script قابل للتنفيذ
        if "if __name__ == '__main__':" in content:
            return "script"
        
        # فحص إذا كان test file
        if "test_" in file_name or "_test.py" in file_name or "tests" in str(file_path):
            return "test"
        
        # فحص إذا كان config file
        if any(config in file_name.lower() for config in ["config", "settings", "conf"]):
            return "config"
        
        return "module"
    
    def analyze_dependencies(self):
        """تحليل تبعيات Python"""
        dependencies = {
            "requirements_files": {},
            "imports": {},
            "standard_library": [],
            "third_party": [],
            "local_imports": [],
            "dependency_tree": {}
        }
        
        # تحليل ملفات requirements
        req_files = ["requirements.txt", "requirements-dev.txt", "requirements-test.txt"]
        for req_file in req_files:
            req_path = self.project_path / req_file
            if req_path.exists():
                dependencies["requirements_files"][req_file] = self.parse_requirements_file(req_path)
        
        # تحليل setup.py
        setup_py = self.project_path / "setup.py"
        if setup_py.exists():
            dependencies["setup_py"] = self.parse_setup_py(setup_py)
        
        # تحليل pyproject.toml
        pyproject = self.project_path / "pyproject.toml"
        if pyproject.exists():
            dependencies["pyproject_toml"] = self.parse_pyproject_toml(pyproject)
        
        # تحليل الاستيرادات في الكود
        dependencies["imports"] = self.analyze_imports()
        
        return dependencies
    
    def analyze_imports(self):
        """تحليل الاستيرادات في ملفات Python"""
        imports = {
            "all_imports": Counter(),
            "by_file": {},
            "unused_imports": [],
            "missing_imports": [],
            "circular_imports": []
        }
        
        for py_file in self.project_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                file_imports = self.extract_imports_from_content(content)
                relative_path = str(py_file.relative_to(self.project_path))
                imports["by_file"][relative_path] = file_imports
                
                # عد جميع الاستيرادات
                for imp in file_imports:
                    imports["all_imports"][imp] += 1
                    
            except Exception as e:
                continue
        
        return imports
    
    def extract_imports_from_content(self, content):
        """استخراج الاستيرادات من محتوى الملف"""
        imports = []
        
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
        except SyntaxError:
            # إذا فشل AST، استخدم regex
            import_patterns = [
                r'^import\s+([a-zA-Z_][a-zA-Z0-9_\.]*)',
                r'^from\s+([a-zA-Z_][a-zA-Z0-9_\.]*)\s+import'
            ]
            
            for pattern in import_patterns:
                matches = re.findall(pattern, content, re.MULTILINE)
                imports.extend(matches)
        
        return list(set(imports))  # إزالة التكرارات
    
    def parse_requirements_file(self, file_path):
        """تحليل ملف requirements"""
        requirements = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#') and not line.startswith('-'):
                        # تحليل اسم الحزمة والإصدار
                        match = re.match(r'^([a-zA-Z0-9_-]+)([>=<!=~\[\]]+.*)?', line)
                        if match:
                            package_name = match.group(1)
                            version_spec = match.group(2) or ""
                            requirements.append({
                                "name": package_name,
                                "version": version_spec.strip(),
                                "line": line_num,
                                "raw": line
                            })
        except Exception as e:
            pass
        
        return requirements
    
    def detect_frameworks(self):
        """اكتشاف أطر العمل المستخدمة"""
        detected_frameworks = {}
        
        # فحص الاستيرادات والملفات
        for framework, indicators in self.python_patterns["frameworks"].items():
            score = 0
            evidence = []
            
            # فحص الملفات المميزة
            for indicator in indicators:
                if indicator.endswith('.py'):
                    if (self.project_path / indicator).exists():
                        score += 3
                        evidence.append(f"File: {indicator}")
                elif '.' in indicator:
                    # فحص امتداد الملف
                    if list(self.project_path.rglob(f"*{indicator}")):
                        score += 2
                        evidence.append(f"Extension: {indicator}")
                else:
                    # فحص في الاستيرادات
                    for py_file in self.project_path.rglob("*.py"):
                        try:
                            with open(py_file, 'r', encoding='utf-8') as f:
                                content = f.read()
                                if indicator in content:
                                    score += 1
                                    evidence.append(f"Import in: {py_file.name}")
                                    break
                        except:
                            continue
            
            if score > 0:
                detected_frameworks[framework] = {
                    "confidence": min(score * 20, 100),
                    "evidence": evidence[:5],  # أول 5 أدلة
                    "score": score
                }
        
        return detected_frameworks

    def analyze_code_quality(self):
        """تحليل جودة الكود Python"""
        quality = {
            "docstring_coverage": 0,
            "comment_ratio": 0,
            "complexity_metrics": {},
            "naming_conventions": {},
            "code_smells": [],
            "pep8_compliance": {}
        }

        total_functions = 0
        documented_functions = 0
        total_lines = 0
        comment_lines = 0

        for py_file in self.project_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # تحليل AST
                tree = ast.parse(content)

                # عد الدوال والتوثيق
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                        total_functions += 1
                        if ast.get_docstring(node):
                            documented_functions += 1

                # عد الأسطر والتعليقات
                lines = content.splitlines()
                total_lines += len(lines)

                for line in lines:
                    if line.strip().startswith('#'):
                        comment_lines += 1

            except Exception as e:
                continue

        # حساب النسب
        if total_functions > 0:
            quality["docstring_coverage"] = (documented_functions / total_functions) * 100

        if total_lines > 0:
            quality["comment_ratio"] = (comment_lines / total_lines) * 100

        return quality

    def analyze_virtual_environment(self):
        """تحليل البيئة الافتراضية"""
        venv_info = {
            "detected": False,
            "type": None,
            "path": None,
            "python_version": None,
            "packages_count": 0
        }

        # فحص أنواع البيئات الافتراضية
        for venv_name in self.python_patterns["virtual_envs"]:
            venv_path = self.project_path / venv_name
            if venv_path.exists() and venv_path.is_dir():
                venv_info["detected"] = True
                venv_info["type"] = venv_name
                venv_info["path"] = str(venv_path)

                # محاولة الحصول على معلومات إضافية
                site_packages = venv_path / "lib" / "python*" / "site-packages"
                if list(venv_path.rglob("site-packages")):
                    packages = list(venv_path.rglob("site-packages/*"))
                    venv_info["packages_count"] = len(packages)
                break

        return venv_info

    def analyze_testing_setup(self):
        """تحليل إعداد الاختبارات"""
        testing = {
            "framework": None,
            "test_files": [],
            "coverage": 0,
            "test_directories": [],
            "config_files": []
        }

        # اكتشاف إطار الاختبار
        test_frameworks = {
            "pytest": ["pytest.ini", "conftest.py", "test_*.py"],
            "unittest": ["test_*.py", "*_test.py"],
            "nose": ["nosetests", ".noserc"],
            "tox": ["tox.ini"]
        }

        for framework, indicators in test_frameworks.items():
            for indicator in indicators:
                if indicator.endswith('.py'):
                    if list(self.project_path.rglob(indicator)):
                        testing["framework"] = framework
                        break
                else:
                    if (self.project_path / indicator).exists():
                        testing["framework"] = framework
                        testing["config_files"].append(indicator)
                        break

        # جمع ملفات الاختبار
        test_patterns = ["test_*.py", "*_test.py", "tests.py"]
        for pattern in test_patterns:
            test_files = list(self.project_path.rglob(pattern))
            for test_file in test_files:
                relative_path = test_file.relative_to(self.project_path)
                testing["test_files"].append(str(relative_path))

        # البحث عن مجلدات الاختبار
        test_dirs = ["tests", "test", "testing"]
        for test_dir in test_dirs:
            test_path = self.project_path / test_dir
            if test_path.exists() and test_path.is_dir():
                testing["test_directories"].append(test_dir)

        return testing

    def analyze_documentation(self):
        """تحليل التوثيق"""
        docs = {
            "readme_files": [],
            "doc_directories": [],
            "docstring_style": None,
            "api_docs": False,
            "changelog": False
        }

        # البحث عن ملفات README
        readme_patterns = ["README*", "readme*"]
        for pattern in readme_patterns:
            readme_files = list(self.project_path.glob(pattern))
            for readme_file in readme_files:
                docs["readme_files"].append(readme_file.name)

        # البحث عن مجلدات التوثيق
        doc_dirs = ["docs", "doc", "documentation", "sphinx"]
        for doc_dir in doc_dirs:
            doc_path = self.project_path / doc_dir
            if doc_path.exists() and doc_path.is_dir():
                docs["doc_directories"].append(doc_dir)

        # فحص CHANGELOG
        changelog_files = ["CHANGELOG*", "HISTORY*", "NEWS*"]
        for pattern in changelog_files:
            if list(self.project_path.glob(pattern)):
                docs["changelog"] = True
                break

        return docs

    def analyze_performance_indicators(self):
        """تحليل مؤشرات الأداء"""
        performance = {
            "large_files": [],
            "complex_functions": [],
            "import_complexity": 0,
            "potential_bottlenecks": []
        }

        for py_file in self.project_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                lines = len(content.splitlines())

                # ملفات كبيرة (أكثر من 500 سطر)
                if lines > 500:
                    performance["large_files"].append({
                        "file": str(py_file.relative_to(self.project_path)),
                        "lines": lines
                    })

                # تحليل AST للدوال المعقدة
                try:
                    tree = ast.parse(content)
                    for node in ast.walk(tree):
                        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                            # حساب تعقيد الدالة (عدد العقد)
                            complexity = len(list(ast.walk(node)))
                            if complexity > 50:  # دالة معقدة
                                performance["complex_functions"].append({
                                    "function": node.name,
                                    "file": str(py_file.relative_to(self.project_path)),
                                    "complexity": complexity,
                                    "line": node.lineno
                                })
                except:
                    pass

            except Exception as e:
                continue

        return performance

    def analyze_security(self):
        """تحليل الأمان"""
        security = {
            "potential_issues": [],
            "hardcoded_secrets": [],
            "unsafe_functions": [],
            "sql_injection_risks": []
        }

        # أنماط أمنية مشكوك فيها
        security_patterns = {
            "hardcoded_passwords": [
                r'password\s*=\s*["\'][^"\']+["\']',
                r'pwd\s*=\s*["\'][^"\']+["\']',
                r'secret\s*=\s*["\'][^"\']+["\']'
            ],
            "unsafe_functions": [
                r'eval\s*\(',
                r'exec\s*\(',
                r'os\.system\s*\(',
                r'subprocess\.call\s*\('
            ],
            "sql_patterns": [
                r'SELECT\s+.*\s+FROM\s+.*\s+WHERE\s+.*%s',
                r'INSERT\s+INTO\s+.*\s+VALUES\s+.*%s',
                r'UPDATE\s+.*\s+SET\s+.*%s'
            ]
        }

        for py_file in self.project_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                relative_path = str(py_file.relative_to(self.project_path))

                # فحص الأنماط الأمنية
                for category, patterns in security_patterns.items():
                    for pattern in patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            security["potential_issues"].append({
                                "file": relative_path,
                                "category": category,
                                "matches": len(matches),
                                "pattern": pattern
                            })

            except Exception as e:
                continue

        return security

    def generate_recommendations(self, analysis_results):
        """إنشاء توصيات بناءً على التحليل"""
        recommendations = []

        # توصيات بناءً على جودة الكود
        quality = analysis_results.get("code_quality", {})
        if quality.get("docstring_coverage", 0) < 50:
            recommendations.append({
                "type": "documentation",
                "priority": "medium",
                "title": "تحسين توثيق الكود",
                "description": f"نسبة توثيق الدوال {quality.get('docstring_coverage', 0):.1f}% - يُنصح بزيادتها إلى 80% على الأقل",
                "action": "إضافة docstrings للدوال والكلاسات"
            })

        # توصيات بناءً على الاختبارات
        testing = analysis_results.get("testing", {})
        if not testing.get("test_files"):
            recommendations.append({
                "type": "testing",
                "priority": "high",
                "title": "إضافة اختبارات",
                "description": "لم يتم العثور على ملفات اختبار في المشروع",
                "action": "إنشاء ملفات اختبار باستخدام pytest أو unittest"
            })

        # توصيات بناءً على البيئة الافتراضية
        venv = analysis_results.get("virtual_environment", {})
        if not venv.get("detected"):
            recommendations.append({
                "type": "environment",
                "priority": "medium",
                "title": "إنشاء بيئة افتراضية",
                "description": "لم يتم العثور على بيئة افتراضية",
                "action": "إنشاء بيئة افتراضية باستخدام venv أو virtualenv"
            })

        # توصيات أمنية
        security = analysis_results.get("security_analysis", {})
        if security.get("potential_issues"):
            recommendations.append({
                "type": "security",
                "priority": "high",
                "title": "مراجعة المشاكل الأمنية",
                "description": f"تم العثور على {len(security['potential_issues'])} مشكلة أمنية محتملة",
                "action": "مراجعة وإصلاح المشاكل الأمنية المكتشفة"
            })

        return recommendations

    def display_analysis_summary(self, results):
        """عرض ملخص التحليل"""
        print("\n" + "🐍" * 50)
        print("🐍 HORUS - ملخص تحليل Python المتقدم")
        print("🐍" * 50)

        metadata = results.get("metadata", {})
        structure = results.get("project_structure", {})
        frameworks = results.get("frameworks_detected", {})
        quality = results.get("code_quality", {})

        print(f"📁 المشروع: {metadata.get('project_name', 'غير محدد')}")
        print(f"📍 المسار: {metadata.get('project_path', 'غير محدد')}")
        print(f"🕒 وقت التحليل: {metadata.get('analysis_timestamp', 'غير محدد')[:16]}")

        # إحصائيات الملفات
        stats = structure.get("file_statistics", {})
        print(f"\n📊 إحصائيات الملفات:")
        print(f"  🐍 ملفات Python: {stats.get('total_python_files', 0)}")
        print(f"  📦 حزم: {stats.get('total_packages', 0)}")
        print(f"  📄 وحدات: {stats.get('total_modules', 0)}")
        print(f"  🔧 سكريبتات: {stats.get('total_scripts', 0)}")
        print(f"  📏 إجمالي الأسطر: {structure.get('total_lines', 0):,}")

        # أطر العمل المكتشفة
        if frameworks:
            print(f"\n🚀 أطر العمل المكتشفة:")
            for framework, info in list(frameworks.items())[:5]:
                print(f"  • {framework}: {info['confidence']}% ثقة")

        # جودة الكود
        print(f"\n📈 جودة الكود:")
        print(f"  📝 توثيق الدوال: {quality.get('docstring_coverage', 0):.1f}%")
        print(f"  💬 نسبة التعليقات: {quality.get('comment_ratio', 0):.1f}%")

        # التوصيات
        recommendations = results.get("recommendations", [])
        if recommendations:
            print(f"\n💡 التوصيات ({len(recommendations)}):")
            for i, rec in enumerate(recommendations[:3], 1):
                priority_emoji = "🔴" if rec["priority"] == "high" else "🟡" if rec["priority"] == "medium" else "🟢"
                print(f"  {i}. {priority_emoji} {rec['title']}")

        print("🐍" * 50)

def main():
    """الدالة الرئيسية"""
    import argparse

    parser = argparse.ArgumentParser(description="🐍 HORUS - محلل Python المتقدم")
    parser.add_argument("--project", "-p", help="مسار مشروع Python")

    args = parser.parse_args()

    # تحديد مسار المشروع
    project_path = args.project if args.project else "."

    # إنشاء المحلل
    analyzer = HorusPythonAnalyzer(project_path)

    # تشغيل التحليل
    results = analyzer.analyze_full_python_project()

    # عرض النتائج
    analyzer.display_analysis_summary(results)

    return results

if __name__ == "__main__":
    main()
