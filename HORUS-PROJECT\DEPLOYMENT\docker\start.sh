#!/bin/bash
# HORUS AI Agent Docker Start Script

set -e  # Exit on any error

echo "🤖 Starting HORUS AI Agent Container..."
echo "=================================="

# Function to check if <PERSON><PERSON><PERSON> is ready
check_ollama() {
    curl -s http://localhost:11434/api/tags > /dev/null 2>&1
}

# Function to cleanup on exit
cleanup() {
    echo "🛑 Shutting down services..."
    if [ ! -z "$OLLAMA_PID" ]; then
        kill $OLLAMA_PID 2>/dev/null || true
    fi
    exit 0
}

# Set trap for cleanup
trap cleanup SIGTERM SIGINT EXIT

# Start Ollama in background
echo "🔄 Starting Ollama service..."
ollama serve > /app/LOGS/ollama.log 2>&1 &
OLLAMA_PID=$!

# Wait for Ollama to be ready
echo "⏳ Waiting for Ollama to be ready..."
for i in {1..30}; do
    if check_ollama; then
        echo "✅ Ollama is ready!"
        break
    fi
    echo "   Attempt $i/30..."
    sleep 2
done

if ! check_ollama; then
    echo "❌ Ollama failed to start after 60 seconds"
    exit 1
fi

# Check if Mistral model exists (skip if SKIP_MODEL_DOWNLOAD is set)
if [ "$SKIP_MODEL_DOWNLOAD" = "true" ]; then
    echo "⏭️ Skipping model download (using local models)"
    echo "📋 Available models:"
    ollama list
else
    echo "📥 Checking for Mistral 7B model..."
    if ! ollama list | grep -q "mistral:7b"; then
        echo "📦 Pulling Mistral 7B model (this may take a while)..."
        ollama pull mistral:7b
        if [ $? -eq 0 ]; then
            echo "✅ Mistral 7B model downloaded successfully!"
        else
            echo "❌ Failed to download Mistral 7B model"
            exit 1
        fi
    else
        echo "✅ Mistral 7B model already available"
    fi
fi

# Test the model
echo "🧪 Testing Mistral 7B model..."
if ollama run mistral:7b "Hello" --timeout 30s > /dev/null 2>&1; then
    echo "✅ Model test successful!"
else
    echo "⚠️ Model test failed, but continuing..."
fi

# Start HORUS Agent
echo "🚀 Starting HORUS AI Agent API Server..."
cd /app

# Create logs directory if not exists
mkdir -p /app/LOGS

# Start the API server
python -m uvicorn CORE.api.api_server:app \
    --host 0.0.0.0 \
    --port 8000 \
    --log-level info \
    --access-log \
    --log-config /dev/null 2>&1 | tee /app/LOGS/horus.log

# This line should not be reached due to the trap
echo "🛑 HORUS AI Agent stopped"
