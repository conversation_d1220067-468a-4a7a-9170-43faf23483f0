version: '3.8'

services:
  horus-agent:
    build:
      context: ../..
      dockerfile: DEPLOYMENT/docker/Dockerfile
    container_name: horus-ai-agent
    hostname: horus-agent
    ports:
      - "8000:8000"
      - "11434:11434"
    volumes:
      - horus_data:/app/CORE/memory
      - horus_reports:/app/REPORTS
      - horus_config:/app/CONFIG
      - horus_logs:/app/LOGS
      - horus_models:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
      - PYTHONPATH=/app
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - OLLAMA_MODELS=/root/.ollama/models
      - OLLAMA_KEEP_ALIVE=24h
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/status"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 12G
        reservations:
          memory: 8G
    networks:
      - horus-network

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: horus-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - horus_ssl:/etc/nginx/ssl
    depends_on:
      - horus-agent
    restart: unless-stopped
    networks:
      - horus-network
    profiles:
      - with-nginx

volumes:
  horus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/memory
  horus_reports:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/reports
  horus_config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/config
  horus_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/logs
  horus_models:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/models
  horus_ssl:
    driver: local

networks:
  horus-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
