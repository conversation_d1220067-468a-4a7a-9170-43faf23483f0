#!/usr/bin/env python3
"""
HORUS AI Agent - Quick Start
بدء سريع لوكيل HORUS الذكي
"""
import sys
import asyncio
from pathlib import Path

# Add paths for new structure
sys.path.insert(0, str(Path(__file__).parent))
sys.path.insert(0, str(Path(__file__).parent / "CORE"))

try:
    from CORE.agent.horus_agent import horus_agent
    from CORE.memory.memory_manager import memory_manager
    from rich.console import Console
    from rich.panel import Panel
    from rich.prompt import Prompt
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install requirements: pip install -r requirements.txt")
    sys.exit(1)

console = Console()


async def quick_chat():
    """Quick chat interface with HORUS"""
    
    console.print(Panel.fit(
        "🤖 [bold blue]HORUS AI Agent v1.0.0[/bold blue]\n"
        "مدعوم بـ [green]Mistral 7B[/green] محلياً\n"
        "[dim]New Organized Structure[/dim]",
        title="مرحباً"
    ))
    
    # Start the agent
    console.print("[yellow]🔄 بدء تشغيل الوكيل...[/yellow]")
    success = await horus_agent.start()
    
    if not success:
        console.print("[bold red]❌ فشل في تشغيل الوكيل[/bold red]")
        console.print("[dim]تأكد من تشغيل Ollama وتوفر نموذج Mistral 7B[/dim]")
        return
    
    console.print("[bold green]✅ الوكيل جاهز للمحادثة![/bold green]")
    console.print("[dim]اكتب 'خروج' أو 'exit' للإنهاء[/dim]")
    console.print("[dim]اكتب 'help' لعرض الأوامر المتاحة[/dim]\n")
    
    try:
        while True:
            # Get user input
            user_input = Prompt.ask("[bold cyan]أنت")
            
            if user_input.lower() in ['خروج', 'exit', 'quit', 'bye']:
                break
            elif user_input.lower() == 'help':
                show_help()
                continue
            elif user_input.lower() == 'status':
                show_status()
                continue
            
            if user_input.strip():
                # Show thinking
                with console.status("[bold yellow]🤔 يفكر..."):
                    response = await horus_agent.process_message(user_input)
                
                # Display response
                console.print(Panel(
                    response,
                    title="[bold green]HORUS[/bold green]",
                    border_style="green"
                ))
                console.print()  # Empty line for spacing
    
    except KeyboardInterrupt:
        pass
    finally:
        await horus_agent.stop()
        console.print("\n[bold yellow]👋 مع السلامة![/bold yellow]")


def show_help():
    """Show help information"""
    console.print(Panel(
        """[bold cyan]الأوامر المتاحة:[/bold cyan]

🔧 [yellow]أوامر التحليل:[/yellow]
• حلل المشروع - تحليل شامل للمشروع
• حلل الهيكل - تحليل هيكل المشروع
• حلل الجودة - تحليل جودة الكود
• اعرض المحللات - قائمة المحللات المتاحة

💬 [yellow]أوامر عامة:[/yellow]
• help - عرض هذه المساعدة
• status - حالة النظام
• exit/خروج - الخروج من البرنامج

📁 [yellow]أوامر الملفات:[/yellow]
• اقرأ الملف [اسم الملف] - قراءة ملف
• اعرض الملفات - عرض ملفات المجلد
• ابحث عن [نص] - البحث في الملفات""",
        title="[bold blue]مساعدة HORUS[/bold blue]",
        border_style="blue"
    ))


def show_status():
    """Show system status"""
    console.print(Panel(
        f"""[bold cyan]حالة النظام:[/bold cyan]

🤖 [yellow]الوكيل:[/yellow] {'✅ يعمل' if horus_agent.is_running else '❌ متوقف'}
🧠 [yellow]النموذج:[/yellow] Mistral 7B
💾 [yellow]المحادثات:[/yellow] {len(memory_manager.conversations)}
📁 [yellow]المجلد الحالي:[/yellow] {Path.cwd().name}
🏗️ [yellow]الهيكل:[/yellow] منظم ومحدث""",
        title="[bold green]حالة HORUS[/bold green]",
        border_style="green"
    ))


def main():
    """Main entry point"""
    try:
        asyncio.run(quick_chat())
    except KeyboardInterrupt:
        console.print("\n[yellow]تم الإنهاء بواسطة المستخدم[/yellow]")
    except Exception as e:
        console.print(f"[bold red]خطأ: {e}[/bold red]")


if __name__ == "__main__":
    main()
