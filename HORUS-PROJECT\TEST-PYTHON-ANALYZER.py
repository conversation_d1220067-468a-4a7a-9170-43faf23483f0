#!/usr/bin/env python3
"""
🐍 اختبار محلل Python المتقدم في نظام HORUS
Test Advanced Python Analyzer in HORUS System
"""

import os
import sys
from pathlib import Path

# إضافة مسار HORUS للاستيراد
sys.path.append(str(Path(__file__).parent))

def test_python_analyzer():
    """اختبار محلل Python المتقدم"""
    print("🐍" * 50)
    print("🐍 اختبار محلل Python المتقدم في HORUS")
    print("🐍" * 50)
    
    try:
        from ANALYZERS.python_analyzer import HorusPythonAnalyzer
        
        print("✅ تم استيراد محلل Python بنجاح")
        
        # اختبار على المشروع الحالي
        print("\n🔍 اختبار التحليل على مشروع HORUS نفسه...")
        analyzer = HorusPythonAnalyzer(".")
        
        print(f"📁 المشروع: {analyzer.project_name}")
        print(f"📍 المسار: {analyzer.project_path}")
        
        # تشغيل التحليل الكامل
        print("\n🚀 بدء التحليل المتقدم...")
        results = analyzer.analyze_full_python_project()
        
        if results:
            print("✅ تم التحليل بنجاح!")
            
            # عرض ملخص النتائج
            analyzer.display_analysis_summary(results)
            
            # عرض تفاصيل إضافية
            print("\n📊 تفاصيل إضافية:")
            
            # هيكل المشروع
            structure = results.get("project_structure", {})
            stats = structure.get("file_statistics", {})
            print(f"  📄 ملفات Python: {stats.get('total_python_files', 0)}")
            print(f"  📦 حزم Python: {stats.get('total_packages', 0)}")
            print(f"  🔧 سكريبتات: {stats.get('total_scripts', 0)}")
            
            # أطر العمل
            frameworks = results.get("frameworks_detected", {})
            if frameworks:
                print(f"  🚀 أطر العمل المكتشفة: {len(frameworks)}")
                for framework, info in list(frameworks.items())[:3]:
                    print(f"    • {framework}: {info['confidence']}%")
            
            # التبعيات
            dependencies = results.get("dependencies", {})
            imports = dependencies.get("imports", {})
            all_imports = imports.get("all_imports", {})
            if all_imports:
                print(f"  📦 إجمالي الاستيرادات: {len(all_imports)}")
                top_imports = list(all_imports.most_common(3))
                for module, count in top_imports:
                    print(f"    • {module}: {count} مرة")
            
            # جودة الكود
            quality = results.get("code_quality", {})
            print(f"  📈 توثيق الدوال: {quality.get('docstring_coverage', 0):.1f}%")
            print(f"  💬 نسبة التعليقات: {quality.get('comment_ratio', 0):.1f}%")
            
            # الاختبارات
            testing = results.get("testing", {})
            test_files = testing.get("test_files", [])
            print(f"  🧪 ملفات الاختبار: {len(test_files)}")
            if testing.get("framework"):
                print(f"  🔬 إطار الاختبار: {testing['framework']}")
            
            # البيئة الافتراضية
            venv = results.get("virtual_environment", {})
            if venv.get("detected"):
                print(f"  🌐 بيئة افتراضية: {venv['type']} ✅")
            else:
                print(f"  🌐 بيئة افتراضية: غير موجودة ❌")
            
            # التوصيات
            recommendations = results.get("recommendations", [])
            print(f"  💡 التوصيات: {len(recommendations)}")
            
            return True
        else:
            print("❌ فشل في التحليل")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_specific_features():
    """اختبار ميزات محددة"""
    print("\n🔧 اختبار ميزات محددة...")
    
    try:
        from ANALYZERS.python_analyzer import HorusPythonAnalyzer
        
        analyzer = HorusPythonAnalyzer(".")
        
        # اختبار اكتشاف أطر العمل
        print("\n🚀 اختبار اكتشاف أطر العمل...")
        frameworks = analyzer.detect_frameworks()
        print(f"  تم اكتشاف {len(frameworks)} إطار عمل")
        
        # اختبار تحليل الهيكل
        print("\n🏗️ اختبار تحليل هيكل المشروع...")
        structure = analyzer.analyze_project_structure()
        print(f"  تم تحليل {structure['file_statistics']['total_python_files']} ملف Python")
        
        # اختبار تحليل التبعيات
        print("\n📦 اختبار تحليل التبعيات...")
        dependencies = analyzer.analyze_dependencies()
        imports = dependencies.get("imports", {})
        print(f"  تم تحليل {len(imports.get('all_imports', {}))} استيراد")
        
        # اختبار تحليل جودة الكود
        print("\n📈 اختبار تحليل جودة الكود...")
        quality = analyzer.analyze_code_quality()
        print(f"  توثيق الدوال: {quality.get('docstring_coverage', 0):.1f}%")
        
        # اختبار تحليل الاختبارات
        print("\n🧪 اختبار تحليل الاختبارات...")
        testing = analyzer.analyze_testing_setup()
        print(f"  ملفات الاختبار: {len(testing.get('test_files', []))}")
        
        # اختبار تحليل الأمان
        print("\n🔒 اختبار تحليل الأمان...")
        security = analyzer.analyze_security()
        issues = security.get("potential_issues", [])
        print(f"  مشاكل أمنية محتملة: {len(issues)}")
        
        print("✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات: {e}")
        return False

def test_integration_with_main_analyzer():
    """اختبار التكامل مع المحلل الرئيسي"""
    print("\n🔗 اختبار التكامل مع المحلل الرئيسي...")
    
    try:
        from ANALYZERS.project_analyzer import HorusProjectAnalyzer
        
        # إنشاء محلل رئيسي
        main_analyzer = HorusProjectAnalyzer(".")
        
        print("🚀 تشغيل التحليل الكامل مع التكامل...")
        results = main_analyzer.run_full_analysis()
        
        if results:
            print("✅ التكامل يعمل بنجاح!")
            
            # فحص إذا تم استخدام محلل Python المتقدم
            if "python_analysis" in results:
                print("🐍 تم استخدام محلل Python المتقدم!")
                python_results = results["python_analysis"]
                
                # عرض بعض النتائج
                frameworks = python_results.get("frameworks_detected", {})
                if frameworks:
                    print(f"  🚀 أطر العمل: {list(frameworks.keys())[:3]}")
                
                structure = python_results.get("project_structure", {})
                stats = structure.get("file_statistics", {})
                print(f"  📄 ملفات Python: {stats.get('total_python_files', 0)}")
                
            else:
                print("ℹ️ تم استخدام التحليل العام")
            
            return True
        else:
            print("❌ فشل في التحليل المتكامل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🦅 HORUS - اختبار محلل Python المتقدم")
    print("=" * 60)
    
    # تشغيل الاختبارات
    tests = [
        ("اختبار محلل Python الأساسي", test_python_analyzer),
        ("اختبار الميزات المحددة", test_specific_features),
        ("اختبار التكامل", test_integration_with_main_analyzer)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    # النتيجة النهائية
    print("\n" + "🦅" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! محلل Python جاهز للاستخدام")
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج مراجعة")
    
    print("🦅" * 50)

if __name__ == "__main__":
    main()
