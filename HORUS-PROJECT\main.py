"""
HORUS AI Agent - Main Entry Point
Local AI Agent powered by Mistral 7B via Ollama
"""
import asyncio
import sys
import os
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from cli import app as cli_app


def main():
    """Main entry point"""
    print("🤖 HORUS AI Agent")
    print("=" * 50)
    
    # Check if Ollama is available
    try:
        import subprocess
        result = subprocess.run(
            ["ollama", "--version"], 
            capture_output=True, 
            text=True, 
            timeout=5
        )
        if result.returncode != 0:
            print("❌ Ollama not found. Please install Ollama first.")
            print("   Visit: https://ollama.ai")
            return
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Ollama not found. Please install Ollama first.")
        print("   Visit: https://ollama.ai")
        return
    
    # Run CLI
    cli_app()


if __name__ == "__main__":
    main()
